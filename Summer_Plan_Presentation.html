<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Summer 2024 Cybersecurity Strategic Plan</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow-x: hidden;
        }

        .presentation-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .slide {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 30px 0;
            padding: 40px;
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .title-slide {
            text-align: center;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 60px 40px;
        }

        .title-slide h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .title-slide .subtitle {
            font-size: 1.3em;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .title-slide .meta {
            font-size: 1.1em;
            opacity: 0.8;
        }

        h2 {
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 25px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        h3 {
            color: #34495e;
            font-size: 1.5em;
            margin: 20px 0 15px 0;
        }

        .highlight-box {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #2c3e50;
            display: block;
        }

        .stat-label {
            font-size: 1.1em;
            color: #34495e;
            margin-top: 10px;
        }

        .timeline {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 25px 0;
        }

        .timeline-item {
            display: flex;
            margin: 20px 0;
            align-items: center;
        }

        .timeline-marker {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .timeline-content {
            flex: 1;
        }

        .timeline-title {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.2em;
        }

        .timeline-desc {
            color: #7f8c8d;
            margin-top: 5px;
        }

        .objectives-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }

        .objective-card {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #e17055;
        }

        .objective-icon {
            font-size: 2em;
            margin-bottom: 15px;
        }

        .risk-table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }

        .risk-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: left;
        }

        .risk-table td {
            padding: 15px;
            border-bottom: 1px solid #ecf0f1;
        }

        .risk-table tr:hover {
            background: #f8f9fa;
        }

        .cta-section {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            text-align: center;
            padding: 40px;
            border-radius: 15px;
            margin: 30px 0;
        }

        .cta-button {
            background: white;
            color: #11998e;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s ease;
            margin: 20px 10px;
        }

        .cta-button:hover {
            transform: scale(1.05);
        }

        .progress-bar {
            background: #ecf0f1;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100%;
            border-radius: 10px;
            transition: width 0.8s ease;
        }

        .footer {
            text-align: center;
            padding: 30px;
            color: white;
            font-size: 0.9em;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .presentation-container {
                padding: 10px;
            }

            .slide {
                padding: 20px;
            }

            .title-slide h1 {
                font-size: 2em;
            }

            h2 {
                font-size: 1.8em;
            }
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <!-- Title Slide -->
        <div class="slide title-slide">
            <h1>🛡️ Summer 2024 Cybersecurity Strategic Plan</h1>
            <div class="subtitle">Leveraging Optimal Timing for Critical Security Enhancements</div>
            <div class="meta">
                <p><strong>Duration:</strong> 68 working days (May 27 - August 30, 2024)</p>
                <p><strong>Resource Allocation:</strong> 408 focused work hours</p>
                <p><strong>Efficiency Target:</strong> 99% utilization</p>
            </div>
        </div>

        <!-- Executive Summary -->
        <div class="slide">
            <h2>📊 Executive Summary</h2>
            <div class="highlight-box">
                <h3>🎯 The Strategic Opportunity</h3>
                <p>Summer presents a unique window for transformative security improvements with minimal business disruption. This comprehensive plan leverages 408 focused work hours during the optimal low-activity period to implement critical security enhancements.</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">99%</span>
                    <div class="stat-label">Resource Efficiency</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">95%</span>
                    <div class="stat-label">Vulnerability Closure</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">100%</span>
                    <div class="stat-label">Compliance Achievement</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">50+</span>
                    <div class="stat-label">AD Security Hours</div>
                </div>
            </div>
        </div>

        <!-- Strategic Objectives -->
        <div class="slide">
            <h2>🎯 Strategic Objectives</h2>
            <div class="objectives-grid">
                <div class="objective-card">
                    <div class="objective-icon">🛡️</div>
                    <h3>Vulnerability Management</h3>
                    <p><strong>Key Tickets:</strong></p>
                    <ul style="margin: 10px 0; font-size: 0.9em;">
                        <li>SR063746 - Java verify</li>
                        <li>SR065818 - Java vuln monitoring</li>
                        <li>SR0636xx series - Windows patches (RMCAST, VS RCE, Animation timeline, Memory Corruption, Heap buffer, .NET)</li>
                    </ul>
                </div>
                <div class="objective-card">
                    <div class="objective-icon">🔐</div>
                    <h3>Active Directory Security</h3>
                    <p><strong>Key Tickets:</strong></p>
                    <ul style="margin: 10px 0; font-size: 0.9em;">
                        <li>SR056693 - AD Tiering (50 hours)</li>
                        <li>SR056687 - Credential Guard</li>
                        <li>SR056686 - Domain Protected Users</li>
                        <li>SR056013 - PenTest Report analysis</li>
                        <li>SR056001 - IBM AD Audit review</li>
                    </ul>
                </div>
                <div class="objective-card">
                    <div class="objective-icon">📋</div>
                    <h3>Compliance & Documentation</h3>
                    <p><strong>Key Tickets:</strong></p>
                    <ul style="margin: 10px 0; font-size: 0.9em;">
                        <li>SR063090 - M365 PIA redo</li>
                        <li>SR054039 - Quicken PIA</li>
                        <li>SR053454 - Survey Monkey PIA</li>
                        <li>SR053388 - Solar Winds PIA</li>
                        <li>SR053346 - Poe PIA</li>
                        <li>SR044136 - Cyber Insurance update</li>
                        <li>SR056166 - Risk Management Plan</li>
                    </ul>
                </div>
                <div class="objective-card">
                    <div class="objective-icon">👁️</div>
                    <h3>Threat Monitoring</h3>
                    <p><strong>Key Tickets:</strong></p>
                    <ul style="margin: 10px 0; font-size: 0.9em;">
                        <li>Arctic Wolf Reports (May-Aug processing)</li>
                        <li>SR064903 - AW Agents follow-up</li>
                        <li>SR064901 - AW Parent items</li>
                        <li>SR064912 - AW Scan missing hosts</li>
                        <li>SR064906 - CISA KEV review</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Timeline Overview -->
        <div class="slide">
            <h2>📅 Implementation Timeline</h2>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-marker">1</div>
                    <div class="timeline-content">
                        <div class="timeline-title">May Week 4: Quick Wins & Foundation</div>
                        <div class="timeline-desc">
                            <strong>Priority Tickets:</strong>
                            <ul style="margin: 8px 0; font-size: 0.9em;">
                                <li>SR063746 - Java verify</li>
                                <li>SR063640 - Lashburn imaging</li>
                                <li>SR063504/SR063498/SR063298 - User status updates</li>
                                <li>SR065818 - Java vuln monitoring</li>
                                <li>SR064871 - MDI Brute Force investigation</li>
                                <li>Arctic Wolf May reports processing</li>
                            </ul>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%;"></div>
                        </div>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker">2</div>
                    <div class="timeline-content">
                        <div class="timeline-title">June: Vulnerability Management & Research</div>
                        <div class="timeline-desc">
                            <strong>Windows Patches:</strong> SR0636xx series (RMCAST, VS RCE, Animation timeline, Memory Corruption, Heap buffer, .NET)
                            <br><strong>AD Research:</strong> SR056013 (PenTest), SR056001 (IBM AD Audit), SR056687 (Credential Guard), SR056686 (Domain Protected Users)
                            <br><strong>Arctic Wolf:</strong> SR064903, SR064901, SR064912, SR064906, SR064021, SR063622
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%;"></div>
                        </div>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker">3</div>
                    <div class="timeline-content">
                        <div class="timeline-title">July: Infrastructure & Policy Focus</div>
                        <div class="timeline-desc">
                            <strong>Infrastructure:</strong> SR064339 (Firewall Cutover Lakeview), SR053742 (Secured Core/Memory Integrity Hyper-V), SR056787 (Pre-pentest tools/PingCastle), SR063544 (Dell Server iDRAC)
                            <br><strong>PIAs:</strong> SR063090 (M365), SR054039 (Quicken), SR053454 (Survey Monkey), SR053388 (Solar Winds), SR053346 (Poe)
                            <br><strong>Policy:</strong> SR044136 (Cyber Insurance), SR056166 (Risk Management Plan)
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 50%;"></div>
                        </div>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker">4</div>
                    <div class="timeline-content">
                        <div class="timeline-title">August: AD Tiering Implementation</div>
                        <div class="timeline-desc">
                            <strong>Primary Focus:</strong> SR056693 (AD Tiering - 50 hours, 5 phases)
                            <br><strong>Parallel Work:</strong> SR059501 (AW MSA Manager), SR055383/SR064370 (CA reviews), SR055297 (Teams), SR055296 (Viva Engage), SR054535 (Windows Hello), SR054534 (Clever Badges)
                            <br><strong>Ongoing:</strong> Arctic Wolf August reports processing
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 25%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- Resource Requirements -->
        <div class="slide">
            <h2>🔧 Resource Requirements</h2>
            <div class="objectives-grid">
                <div class="objective-card">
                    <div class="objective-icon">👥</div>
                    <h3>Internal Resources</h3>
                    <ul>
                        <li><strong>408 hours</strong> dedicated cybersecurity analyst</li>
                        <li><strong>IT team coordination</strong> for infrastructure changes</li>
                        <li><strong>Management approval</strong> for change windows</li>
                    </ul>
                </div>
                <div class="objective-card">
                    <div class="objective-icon">🤝</div>
                    <h3>External Dependencies</h3>
                    <ul>
                        <li><strong>AD tiering contractor</strong> (August engagement)</li>
                        <li><strong>Vendor support</strong> coordination</li>
                        <li><strong>Hardware refresh</strong> requirements</li>
                    </ul>
                </div>
                <div class="objective-card">
                    <div class="objective-icon">💰</div>
                    <h3>Budget Impact</h3>
                    <ul>
                        <li><strong>Contractor costs:</strong> $15,000 for AD tiering</li>
                        <li><strong>Tool licensing:</strong> $3,000 for security tools</li>
                        <li><strong>Hardware:</strong> $8,000 for infrastructure updates</li>
                    </ul>
                </div>
            </div>
        </div>







        <div class="footer">
            <p>🛡️ Summer 2024 Cybersecurity Strategic Plan | Informational Briefing</p>
            <p>Estimated briefing time: 10-15 minutes</p>
        </div>
    </div>

    <script>
        // Add some interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Animate progress bars
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });

            // Add hover effects to stat cards
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Smooth scrolling for better presentation flow
            document.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowDown' || e.key === ' ') {
                    e.preventDefault();
                    window.scrollBy({
                        top: window.innerHeight,
                        behavior: 'smooth'
                    });
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    window.scrollBy({
                        top: -window.innerHeight,
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
</body>
</html>
